"""
文化符号分析Agent使用示例
展示如何使用系统进行完整的文化符号分析流程
"""

import time
from cultural_symbol_agent import CulturalSymbolAgent
from config import get_config
from logger import get_logger

def example_complete_workflow():
    """完整工作流程示例"""
    print("🎯 文化符号分析Agent - 完整工作流程示例")
    print("=" * 60)
    
    # 初始化
    logger = get_logger()
    config = get_config()
    
    logger.info("开始文化符号分析示例")
    
    try:
        # 创建Agent
        print("🚀 正在初始化Agent...")
        agent = CulturalSymbolAgent()
        print("✅ Agent初始化成功！")
        
        # 显示配置信息
        if config.system.debug:
            config.print_config()
        
        print("\n" + "=" * 60)
        print("📋 开始分析流程")
        print("=" * 60)
        
        # 步骤1: 输入客户名称
        print("\n🔸 步骤1: 输入客户名称")
        user_input = "我想分析苹果公司的文化符号"
        print(f"👤 用户: {user_input}")
        
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        
        # 步骤2: 收集基本信息
        print("\n🔸 步骤2: 收集基本信息")
        user_input = "苹果公司是一家全球知名的科技公司，主要设计和销售消费电子产品"
        print(f"👤 用户: {user_input}")
        
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        
        # 步骤3: 请求生成问题
        print("\n🔸 步骤3: 生成收集信息的问题")
        user_input = "请生成3个问题来收集更多信息"
        print(f"👤 用户: {user_input}")
        
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        
        # 步骤4: 回答问题
        print("\n🔸 步骤4: 回答收集信息的问题")
        
        answers = [
            "苹果公司的核心业务是设计、制造和销售iPhone、iPad、Mac、Apple Watch等消费电子产品，以及提供相关的软件和服务",
            "苹果的目标客户是追求高品质、创新设计和用户体验的消费者，包括专业人士、创意工作者和技术爱好者",
            "苹果希望传达简约、创新、高品质、用户友好的品牌价值，强调'Think Different'的理念"
        ]
        
        for i, answer in enumerate(answers, 1):
            print(f"\n📝 回答问题{i}:")
            print(f"👤 用户: {answer}")
            response = agent.run(answer)
            print(f"🤖 助手: {response}")
            time.sleep(1)  # 模拟思考时间
        
        # 步骤5: 生成文化符号总结
        print("\n🔸 步骤5: 生成文化符号总结")
        user_input = "请基于收集的信息生成文化符号总结"
        print(f"👤 用户: {user_input}")
        
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        
        # 步骤6: 结果确认
        print("\n🔸 步骤6: 结果确认")
        user_input = "我对这个分析结果很满意"
        print(f"👤 用户: {user_input}")
        
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        
        # 显示最终状态
        print("\n" + "=" * 60)
        print("📊 最终分析结果")
        print("=" * 60)
        
        final_info = agent.get_customer_info()
        print(f"🏢 客户名称: {final_info.get('name', '未设置')}")
        print(f"📁 收集资料: {len(final_info.get('materials', []))} 项")
        print(f"❓ 问答记录: {len(final_info.get('questions_answers', {}))} 个")
        print(f"🎨 文化符号: {len(final_info.get('cultural_symbols', []))} 个")
        
        if final_info.get('cultural_symbols'):
            print("\n🎨 文化符号详情:")
            for i, symbol in enumerate(final_info['cultural_symbols'], 1):
                print(f"  {i}. {symbol}")
        
        print("\n✅ 分析流程完成！")
        logger.info("文化符号分析示例完成")
        
    except Exception as e:
        logger.log_error_with_context(e, "执行完整工作流程示例")
        print(f"❌ 示例执行失败: {str(e)}")

def example_interactive_session():
    """交互式会话示例"""
    print("🎯 文化符号分析Agent - 交互式会话示例")
    print("=" * 60)
    
    logger = get_logger()
    
    try:
        agent = CulturalSymbolAgent()
        
        # 模拟多轮对话
        conversations = [
            ("你好", "问候"),
            ("我想分析一个新客户", "开始分析"),
            ("客户是特斯拉公司", "设置客户"),
            ("特斯拉是电动汽车制造商", "添加信息"),
            ("请帮我生成问题", "生成问题"),
            ("特斯拉专注于可持续交通", "回答问题"),
            ("生成文化符号分析", "生成分析"),
            ("谢谢", "结束对话")
        ]
        
        for user_input, description in conversations:
            print(f"\n📝 {description}")
            print(f"👤 用户: {user_input}")
            
            response = agent.run(user_input)
            print(f"🤖 助手: {response}")
            
            # 显示当前状态
            current_state = agent.get_current_state()
            print(f"📊 当前状态: {current_state}")
            
            time.sleep(0.5)  # 模拟对话间隔
        
        print("\n✅ 交互式会话示例完成！")
        
    except Exception as e:
        logger.log_error_with_context(e, "执行交互式会话示例")
        print(f"❌ 示例执行失败: {str(e)}")

def example_error_handling():
    """错误处理示例"""
    print("🎯 文化符号分析Agent - 错误处理示例")
    print("=" * 60)
    
    logger = get_logger()
    
    try:
        agent = CulturalSymbolAgent()
        
        # 测试各种错误情况
        error_cases = [
            ("", "空输入"),
            ("无效的JSON数据: {invalid}", "无效JSON"),
            ("执行不存在的操作", "无效操作"),
            ("非常长的输入" * 1000, "超长输入")
        ]
        
        for user_input, description in error_cases:
            print(f"\n🧪 测试: {description}")
            print(f"👤 用户: {user_input[:50]}{'...' if len(user_input) > 50 else ''}")
            
            try:
                response = agent.run(user_input)
                print(f"🤖 助手: {response}")
            except Exception as e:
                print(f"❌ 处理错误: {str(e)}")
        
        print("\n✅ 错误处理示例完成！")
        
    except Exception as e:
        logger.log_error_with_context(e, "执行错误处理示例")
        print(f"❌ 示例执行失败: {str(e)}")

def example_memory_management():
    """记忆管理示例"""
    print("🎯 文化符号分析Agent - 记忆管理示例")
    print("=" * 60)
    
    logger = get_logger()
    
    try:
        agent = CulturalSymbolAgent()
        
        # 测试记忆功能
        print("📝 测试对话记忆...")
        
        # 添加多轮对话
        for i in range(5):
            user_input = f"这是第{i+1}轮对话"
            print(f"👤 用户: {user_input}")
            
            response = agent.run(user_input)
            print(f"🤖 助手: {response}")
        
        # 检查记忆
        history = agent.get_conversation_history()
        print(f"\n💾 当前记忆中有 {len(history)} 条消息")
        
        # 清空记忆
        print("\n🧹 清空记忆...")
        agent.clear_memory()
        
        history_after_clear = agent.get_conversation_history()
        print(f"💾 清空后记忆中有 {len(history_after_clear)} 条消息")
        
        print("\n✅ 记忆管理示例完成！")
        
    except Exception as e:
        logger.log_error_with_context(e, "执行记忆管理示例")
        print(f"❌ 示例执行失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 文化符号分析Agent - 使用示例集合")
    print("请选择要运行的示例：")
    print("1. 完整工作流程示例")
    print("2. 交互式会话示例")
    print("3. 错误处理示例")
    print("4. 记忆管理示例")
    print("5. 运行所有示例")
    print("6. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            example_complete_workflow()
            break
        elif choice == "2":
            example_interactive_session()
            break
        elif choice == "3":
            example_error_handling()
            break
        elif choice == "4":
            example_memory_management()
            break
        elif choice == "5":
            print("🚀 运行所有示例...")
            example_complete_workflow()
            print("\n" + "="*60 + "\n")
            example_interactive_session()
            print("\n" + "="*60 + "\n")
            example_error_handling()
            print("\n" + "="*60 + "\n")
            example_memory_management()
            break
        elif choice == "6":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    import os
    
    # 检查环境变量
    if not os.getenv("KIMI_API_KEY"):
        print("❌ 错误: 请设置KIMI_API_KEY环境变量")
        print("💡 提示: 复制.env.example为.env并填入您的API密钥")
        exit(1)
    
    main()
