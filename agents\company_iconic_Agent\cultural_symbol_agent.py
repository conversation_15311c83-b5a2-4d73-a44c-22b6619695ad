"""
文化符号分析Agent
使用Lang<PERSON>hain和Kimi-K2实现客户文化符号分析工作流程
"""

import os
import json
from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass
from dotenv import load_dotenv

from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain_core.tools import BaseTool
from langchain.memory import ConversationBufferMemory
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from config import get_config
from logger import get_logger, log_tool_execution, log_function_call

# 加载环境变量
load_dotenv()

class WorkflowState(Enum):
    """工作流程状态枚举"""
    INPUT_CUSTOMER_NAME = "input_customer_name"
    SEARCH_DECISION = "search_decision"
    COLLECT_MATERIALS = "collect_materials"
    FORMAT_PROMPT = "format_prompt"
    ASK_QUESTIONS = "ask_questions"
    GENERATE_SUMMARY = "generate_summary"
    RESULT_SELECTION = "result_selection"
    COMPLETED = "completed"

@dataclass
class CustomerInfo:
    """客户信息数据类"""
    name: str = ""
    materials: List[str] = None
    questions_answers: Dict[str, str] = None
    cultural_symbols: List[str] = None
    
    def __post_init__(self):
        if self.materials is None:
            self.materials = []
        if self.questions_answers is None:
            self.questions_answers = {}
        if self.cultural_symbols is None:
            self.cultural_symbols = []

class CustomerInfoTool(BaseTool):
    """客户信息收集工具"""
    name: str = "customer_info_tool"
    description: str = "用于收集和管理客户相关信息"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 使用私有属性避免Pydantic字段冲突
        self._customer_info = CustomerInfo()
        self._logger = get_logger()

    @log_tool_execution("客户信息工具")
    def _run(self, action: str, data: str = "") -> str:
        """执行客户信息相关操作"""
        try:
            self._logger.log_tool_call(self.name, action)

            if action == "set_name":
                self._customer_info.name = data
                self._logger.info(f"设置客户名称: {data}")
                return f"已设置客户名称：{data}"

            elif action == "add_material":
                self._customer_info.materials.append(data)
                self._logger.info(f"添加客户资料: {data}")
                return f"已添加客户资料：{data}"

            elif action == "add_qa":
                qa_data = json.loads(data)
                self._customer_info.questions_answers.update(qa_data)
                self._logger.info(f"添加问答信息: {len(qa_data)} 项")
                return f"已添加问答信息：{qa_data}"

            elif action == "get_info":
                info = {
                    "name": self._customer_info.name,
                    "materials": self._customer_info.materials,
                    "questions_answers": self._customer_info.questions_answers,
                    "cultural_symbols": self._customer_info.cultural_symbols
                }
                self._logger.debug("获取客户信息")
                return json.dumps(info, ensure_ascii=False)

            elif action == "add_symbol":
                self._customer_info.cultural_symbols.append(data)
                self._logger.info(f"添加文化符号: {data}")
                return f"已添加文化符号：{data}"

            else:
                self._logger.warning(f"不支持的操作: {action}")
                return "不支持的操作"

        except Exception as e:
            self._logger.log_error_with_context(e, f"执行操作: {action}")
            return f"操作失败：{str(e)}"

class WorkflowTool(BaseTool):
    """工作流程控制工具"""
    name: str = "workflow_tool"
    description: str = "用于控制和管理工作流程状态"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._current_state = WorkflowState.INPUT_CUSTOMER_NAME
        self._workflow_history = []
    
    def _run(self, action: str, state: str = "") -> str:
        """执行工作流程操作"""
        try:
            if action == "get_state":
                return self._current_state.value

            elif action == "set_state":
                new_state = WorkflowState(state)
                self._workflow_history.append(self._current_state)
                self._current_state = new_state
                return f"工作流程状态已更新为：{new_state.value}"

            elif action == "get_history":
                return json.dumps([state.value for state in self._workflow_history])

            else:
                return "不支持的操作"

        except Exception as e:
            return f"操作失败：{str(e)}"

class QuestionGeneratorTool(BaseTool):
    """问题生成工具"""
    name: str = "question_generator_tool"
    description: str = "用于生成收集客户信息的问题"
    
    def _run(self, customer_name: str, context: str = "") -> str:
        """生成3个问题来收集客户信息"""
        try:
            questions = [
                f"请描述{customer_name}公司的核心业务和主要产品/服务是什么？",
                f"{customer_name}公司的目标客户群体和市场定位如何？",
                f"{customer_name}公司希望通过文化符号传达什么样的品牌价值和理念？"
            ]
            return json.dumps(questions, ensure_ascii=False)
        except Exception as e:
            return f"问题生成失败：{str(e)}"

class CulturalSymbolAnalyzer(BaseTool):
    """文化符号分析工具"""
    name: str = "cultural_symbol_analyzer"
    description: str = "基于收集的信息分析和生成文化符号"
    
    def _run(self, customer_info: str) -> str:
        """分析客户信息并生成文化符号总结"""
        try:
            # 这里可以集成更复杂的分析逻辑
            info = json.loads(customer_info)
            
            symbols = []
            
            # 基于客户名称分析
            if info.get("name"):
                symbols.append(f"企业标识：{info['name']}")
            
            # 基于收集的资料分析
            if info.get("materials"):
                symbols.append(f"资料要素：{', '.join(info['materials'])}")
            
            # 基于问答信息分析
            if info.get("questions_answers"):
                for q, a in info["questions_answers"].items():
                    if a:
                        symbols.append(f"文化内涵：{a[:50]}...")
            
            return json.dumps(symbols, ensure_ascii=False)
            
        except Exception as e:
            return f"文化符号分析失败：{str(e)}"

class CulturalSymbolAgent:
    """文化符号分析Agent主类"""
    
    def __init__(self):
        """初始化Agent"""
        self.config = get_config()
        if not self.config.validate():
            raise ValueError("配置验证失败，请检查配置文件")

        self.llm = self._init_llm()
        self.memory = ConversationBufferMemory(
            memory_key=self.config.memory.memory_key,
            return_messages=self.config.memory.return_messages,
            k=self.config.memory.k
        )
        self.tools = self._init_tools()
        self.agent_executor = self._init_agent()

    def _init_llm(self) -> ChatOpenAI:
        """初始化Kimi-K2 LLM"""
        return ChatOpenAI(
            model=self.config.llm.model_name,
            openai_api_key=self.config.llm.api_key,
            openai_api_base=self.config.llm.base_url,
            temperature=self.config.llm.temperature,
            max_tokens=self.config.llm.max_tokens,
            timeout=self.config.llm.timeout
        )
    
    def _init_tools(self) -> List[BaseTool]:
        """初始化工具列表"""
        return [
            CustomerInfoTool(),
            WorkflowTool(),
            QuestionGeneratorTool(),
            CulturalSymbolAnalyzer()
        ]
    
    def _init_agent(self) -> AgentExecutor:
        """初始化Agent执行器"""
        prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        agent = create_openai_functions_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=self.memory,
            verbose=self.config.agent.verbose,
            handle_parsing_errors=self.config.agent.handle_parsing_errors,
            max_iterations=self.config.agent.max_iterations,
            early_stopping_method=self.config.agent.early_stopping_method
        )
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个专业的文化符号分析助手，负责帮助用户分析客户的文化符号。

工作流程：
1. 输入客户名称 → 开始搜索
2. 如果需要更多信息 → 提问式收集客户相关资料
3. 按格式填写文化符号提示词，提示用户需上传附件
4. 反问用户3个问题收集信息（非必填）
5. 完成3个文化符号总结
6. 结果选择 → 如果不满意返回步骤5，满意则完成

注意事项：
- 始终保持专业和友好的语调
- 如果工具调用失败，返回"服务暂时不可用，请稍后重试"
- 引用数据来源，确保信息的可追溯性
- 根据当前工作流程状态提供相应的指导

可用工具：
- customer_info_tool: 管理客户信息
- workflow_tool: 控制工作流程状态
- question_generator_tool: 生成收集信息的问题
- cultural_symbol_analyzer: 分析文化符号
"""

    def run(self, user_input: str) -> str:
        """运行Agent处理用户输入"""
        try:
            response = self.agent_executor.invoke({"input": user_input})
            return response.get("output", "抱歉，我无法处理您的请求。")
        except Exception as e:
            print(f"Agent执行错误: {e}")
            return "服务暂时不可用，请稍后重试。"

    def get_conversation_history(self) -> List[BaseMessage]:
        """获取对话历史"""
        return self.memory.chat_memory.messages

    def clear_memory(self):
        """清空对话记忆"""
        self.memory.clear()

    def get_current_state(self) -> str:
        """获取当前工作流程状态"""
        workflow_tool = next((tool for tool in self.tools if tool.name == "workflow_tool"), None)
        if workflow_tool:
            return workflow_tool._run("get_state")
        return "unknown"

    def get_customer_info(self) -> Dict[str, Any]:
        """获取当前客户信息"""
        customer_tool = next((tool for tool in self.tools if tool.name == "customer_info_tool"), None)
        if customer_tool:
            info_str = customer_tool._run("get_info")
            try:
                return json.loads(info_str)
            except:
                return {}
        return {}
