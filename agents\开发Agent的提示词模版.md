### 开发Agent的提示词模版

**工作流程图的结构化描述格式**：用文字或Mermaid语法描述流程图（如果流程图是图片，需先转文字）

---

请根据以下要求，用LangChain开发一个Agent：

1. **工作流程**：
   
   - 用户输入 → 路由节点（判断意图） → 
     ├─ 路径A：如果问天气，调用天气API → 生成回答
     └─ 路径B：如果问数据库，查询SQLite → 生成回答

2. **技术细节**：
   
   - 用OpenAI GPT-4作为LLM。
   - 工具定义：
     - weather_api_tool: 输入{"city": "string"}, 输出{"temperature": int, "condition": string}
     - db_query_tool: 输入{"query": "string"}, 输出{"result": list}

3. **输入输出**：
   
   - 输入：用户自然语言。
   - 输出：自然语言回答，引用数据来源。

4. **错误处理**：
   
   - 如果工具调用失败，返回“服务暂时不可用”。

5. **记忆**：使用ConversationBufferMemory保存最近3轮对话。

请生成完整的Python代码，包含工具定义、Agent初始化、执行逻辑。

---

请根据以下要求，用LangChain开发一个Agent：

1. **工作流程**：
   
   * A([输入客户名称]) --> B{开始搜索}
         B -->|No| C[提问式让用户回答收集客户相关资料]
         B -->|Yes| D[按格式填写文化符号提示词，提示用户需上传附件]
         C --> D
         D --> E{反问用户3个问题收集信息（非必填）}
         E -->|No| F([完成3个文化符号总结])
         E -->|Yes| F
         F --> G{结果选择}
         G -->|No| F
         G -->|Yes| H([完成])

2. **技术细节**：
   
   * 用Kimi-K2作为LLM。

3. **输入输出**：
   
   * 输入：用户自然语言。
   * 输出：自然语言回答，引用数据来源。

4. **错误处理**：
   
   * 如果工具调用失败，返回“服务暂时不可用”。

5. **记忆**：使用ConversationBufferMemory保存最近3轮对话。

请生成完整的Python代码，包含工具定义、Agent初始化、执行逻辑。


