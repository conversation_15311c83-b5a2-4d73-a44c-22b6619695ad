"""
文化符号分析Agent演示脚本
展示完整的文化符号分析工作流程
"""

import os
import time
from cultural_symbol_agent import CulturalSymbolAgent
from config import get_config
from logger import get_logger

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n🔸 步骤{step}: {description}")

def simulate_user_input(user_input, delay=1):
    """模拟用户输入"""
    print(f"👤 用户: {user_input}")
    time.sleep(delay)

def show_agent_response(agent, user_input):
    """显示Agent响应"""
    try:
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        return response
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        return "服务暂时不可用，请稍后重试。"

def show_system_status(agent):
    """显示系统状态"""
    current_state = agent.get_current_state()
    customer_info = agent.get_customer_info()
    
    print(f"\n📊 系统状态:")
    print(f"  🔄 当前状态: {current_state}")
    print(f"  👤 客户名称: {customer_info.get('name', '未设置')}")
    print(f"  📁 资料数量: {len(customer_info.get('materials', []))}")
    print(f"  ❓ 问答数量: {len(customer_info.get('questions_answers', {}))}")
    print(f"  🎨 文化符号: {len(customer_info.get('cultural_symbols', []))}")

def demo_complete_workflow():
    """完整工作流程演示"""
    print_header("文化符号分析Agent - 完整工作流程演示")
    
    logger = get_logger()
    config = get_config()
    
    # 检查配置
    if not config.llm.api_key:
        print("⚠️  注意: 未设置API密钥，将使用模拟模式")
        print("💡 要使用真实API，请在.env文件中设置KIMI_API_KEY")
    
    try:
        print("\n🚀 正在初始化Agent...")
        # 这里我们创建一个模拟版本，避免实际API调用
        print("✅ Agent初始化成功！")
        
        # 模拟Agent（避免实际API调用）
        class MockAgent:
            def __init__(self):
                from cultural_symbol_agent import CustomerInfoTool, WorkflowTool
                self.customer_tool = CustomerInfoTool()
                self.workflow_tool = WorkflowTool()
                self.conversation_count = 0
            
            def run(self, user_input):
                self.conversation_count += 1
                
                if "苹果公司" in user_input or "分析" in user_input:
                    self.customer_tool._run("set_name", "苹果公司")
                    return "好的，我来帮您分析苹果公司的文化符号。请提供一些关于苹果公司的基本信息。"
                
                elif "科技公司" in user_input or "产品" in user_input:
                    self.customer_tool._run("add_material", "苹果公司基本信息")
                    return "已记录苹果公司的基本信息。现在我需要问您几个问题来收集更详细的信息。"
                
                elif "问题" in user_input:
                    return """我为您准备了以下3个问题：
1. 苹果公司的核心业务和主要产品/服务是什么？
2. 苹果公司的目标客户群体和市场定位如何？
3. 苹果公司希望通过文化符号传达什么样的品牌价值和理念？

请逐一回答这些问题。"""
                
                elif "iPhone" in user_input or "核心业务" in user_input:
                    self.customer_tool._run("add_qa", '{"问题1": "' + user_input + '"}')
                    return "感谢您的回答。请继续回答第二个问题。"
                
                elif "消费者" in user_input or "客户群体" in user_input:
                    self.customer_tool._run("add_qa", '{"问题2": "' + user_input + '"}')
                    return "很好。请回答最后一个问题。"
                
                elif "创新" in user_input or "品牌价值" in user_input:
                    self.customer_tool._run("add_qa", '{"问题3": "' + user_input + '"}')
                    return "所有问题都已回答完毕。现在我来为您生成文化符号分析。"
                
                elif "生成" in user_input or "分析" in user_input:
                    symbols = [
                        "🍎 苹果标志 - 简约、现代的品牌识别",
                        "📱 产品设计 - 极简主义、用户友好",
                        "💡 创新理念 - Think Different文化",
                        "🎨 美学追求 - 高品质、精致工艺",
                        "🌍 生态系统 - 无缝连接、整体体验"
                    ]
                    
                    for symbol in symbols:
                        self.customer_tool._run("add_symbol", symbol)
                    
                    return f"""基于收集的信息，我为苹果公司生成了以下文化符号分析：

{chr(10).join(symbols)}

这些文化符号体现了苹果公司的核心价值：简约、创新、高品质和用户体验至上。"""
                
                elif "满意" in user_input:
                    return "太好了！文化符号分析已完成。感谢您使用我们的服务！"
                
                else:
                    return "我理解了您的输入。请继续我们的对话。"
            
            def get_current_state(self):
                return self.workflow_tool._run("get_state")
            
            def get_customer_info(self):
                info_str = self.customer_tool._run("get_info")
                import json
                try:
                    return json.loads(info_str)
                except:
                    return {}
        
        agent = MockAgent()
        
        # 演示对话流程
        conversations = [
            ("你好，我想分析苹果公司的文化符号", "开始分析"),
            ("苹果公司是一家全球知名的科技公司，主要设计和销售消费电子产品", "提供基本信息"),
            ("请生成问题来收集更多信息", "请求生成问题"),
            ("苹果公司的核心业务是设计、制造和销售iPhone、iPad、Mac等产品", "回答问题1"),
            ("苹果的目标客户是追求高品质、创新设计的消费者和专业人士", "回答问题2"),
            ("苹果希望传达简约、创新、高品质的品牌价值，强调Think Different", "回答问题3"),
            ("请基于收集的信息生成文化符号分析", "生成分析"),
            ("我对这个分析结果很满意", "确认结果")
        ]
        
        for i, (user_input, description) in enumerate(conversations, 1):
            print_step(i, description)
            simulate_user_input(user_input)
            show_agent_response(agent, user_input)
            show_system_status(agent)
            
            if i < len(conversations):
                input("\n按Enter继续下一步...")
        
        # 显示最终结果
        print_header("分析结果总结")
        final_info = agent.get_customer_info()
        
        print("🏢 客户信息:")
        print(f"  名称: {final_info.get('name', '未设置')}")
        print(f"  资料: {len(final_info.get('materials', []))} 项")
        print(f"  问答: {len(final_info.get('questions_answers', {}))} 个")
        
        if final_info.get('cultural_symbols'):
            print("\n🎨 文化符号分析:")
            for i, symbol in enumerate(final_info['cultural_symbols'], 1):
                print(f"  {i}. {symbol}")
        
        print("\n✅ 演示完成！")
        logger.info("完整工作流程演示完成")
        
    except Exception as e:
        logger.log_error_with_context(e, "演示过程")
        print(f"❌ 演示失败: {str(e)}")

def demo_interactive_mode():
    """交互模式演示"""
    print_header("交互模式演示")
    
    print("🎮 进入交互模式...")
    print("💡 提示: 这是一个简化的交互演示")
    print("📝 您可以输入以下命令:")
    print("  - 'help': 显示帮助")
    print("  - 'status': 显示状态")
    print("  - 'quit': 退出")
    
    # 简单的交互循环
    while True:
        user_input = input("\n👤 您: ").strip()
        
        if user_input.lower() == 'quit':
            print("👋 再见！")
            break
        elif user_input.lower() == 'help':
            print("🤖 助手: 我可以帮您分析客户的文化符号。请告诉我客户名称开始分析。")
        elif user_input.lower() == 'status':
            print("🤖 助手: 系统运行正常，准备为您服务。")
        elif user_input:
            print("🤖 助手: 我理解了您的输入。在实际使用中，我会根据您的需求提供详细的文化符号分析。")
        else:
            print("🤖 助手: 请输入您的问题或需求。")

def main():
    """主函数"""
    print_header("文化符号分析Agent演示系统")
    
    print("🎯 欢迎使用文化符号分析Agent演示系统！")
    print("\n📋 可用演示:")
    print("1. 完整工作流程演示")
    print("2. 交互模式演示")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择演示模式 (1-3): ").strip()
        
        if choice == "1":
            demo_complete_workflow()
            break
        elif choice == "2":
            demo_interactive_mode()
            break
        elif choice == "3":
            print("👋 感谢使用！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
