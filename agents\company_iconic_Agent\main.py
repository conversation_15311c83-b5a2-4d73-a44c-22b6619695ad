"""
文化符号分析Agent主程序
演示如何使用CulturalSymbolAgent进行客户文化符号分析
"""

import os
import json
from cultural_symbol_agent import CulturalSymbolAgent, WorkflowState

def print_separator():
    """打印分隔线"""
    print("=" * 60)

def print_workflow_status(agent):
    """打印当前工作流程状态"""
    current_state = agent.get_current_state()
    customer_info = agent.get_customer_info()
    
    print(f"\n📊 当前状态: {current_state}")
    if customer_info.get("name"):
        print(f"👤 客户名称: {customer_info['name']}")
    if customer_info.get("materials"):
        print(f"📁 已收集资料: {len(customer_info['materials'])} 项")
    if customer_info.get("questions_answers"):
        print(f"❓ 已回答问题: {len(customer_info['questions_answers'])} 个")
    print()

def demo_workflow():
    """演示完整的工作流程"""
    print("🎯 文化符号分析Agent演示")
    print_separator()
    
    # 初始化Agent
    print("🚀 正在初始化Agent...")
    agent = CulturalSymbolAgent()
    print("✅ Agent初始化完成！")
    print_separator()
    
    # 演示对话流程
    demo_conversations = [
        "你好，我想分析一个客户的文化符号",
        "客户名称是：苹果公司",
        "我需要收集更多资料",
        "苹果公司是一家科技公司，主要产品包括iPhone、iPad、Mac等",
        "请生成问题来收集更多信息",
        "苹果公司的核心业务是设计和销售消费电子产品、软件和在线服务",
        "苹果的目标客户是追求高品质、创新设计的消费者",
        "苹果希望传达简约、创新、高品质的品牌价值",
        "请生成文化符号总结",
        "我对结果满意"
    ]
    
    for i, user_input in enumerate(demo_conversations, 1):
        print(f"👤 用户 ({i}): {user_input}")
        
        # 获取Agent响应
        response = agent.run(user_input)
        print(f"🤖 助手: {response}")
        
        # 显示当前状态
        print_workflow_status(agent)
        print_separator()
        
        # 模拟用户思考时间
        input("按Enter继续下一步...")
        print()

def interactive_mode():
    """交互模式"""
    print("🎯 文化符号分析Agent - 交互模式")
    print("输入 'quit' 退出，输入 'status' 查看状态，输入 'clear' 清空记忆")
    print_separator()
    
    # 初始化Agent
    agent = CulturalSymbolAgent()
    print("✅ Agent已准备就绪！请开始对话...")
    
    while True:
        print_separator()
        user_input = input("👤 您: ").strip()
        
        if user_input.lower() == 'quit':
            print("👋 再见！")
            break
        elif user_input.lower() == 'status':
            print_workflow_status(agent)
            continue
        elif user_input.lower() == 'clear':
            agent.clear_memory()
            print("🧹 对话记忆已清空")
            continue
        elif not user_input:
            continue
        
        # 获取Agent响应
        response = agent.run(user_input)
        print(f"\n🤖 助手: {response}")
        
        # 显示简要状态
        current_state = agent.get_current_state()
        print(f"\n📊 当前状态: {current_state}")

def test_tools():
    """测试工具功能"""
    print("🔧 测试Agent工具功能")
    print_separator()
    
    agent = CulturalSymbolAgent()
    
    # 测试客户信息工具
    print("测试客户信息工具...")
    customer_tool = agent.tools[0]  # CustomerInfoTool
    
    print("设置客户名称:", customer_tool._run("set_name", "测试公司"))
    print("添加资料:", customer_tool._run("add_material", "公司简介"))
    print("添加问答:", customer_tool._run("add_qa", '{"问题1": "答案1"}'))
    print("获取信息:", customer_tool._run("get_info"))
    
    print("\n测试工作流程工具...")
    workflow_tool = agent.tools[1]  # WorkflowTool
    
    print("当前状态:", workflow_tool._run("get_state"))
    print("设置状态:", workflow_tool._run("set_state", "collect_materials"))
    print("获取历史:", workflow_tool._run("get_history"))
    
    print("\n测试问题生成工具...")
    question_tool = agent.tools[2]  # QuestionGeneratorTool
    
    questions = question_tool._run("测试公司")
    print("生成的问题:", questions)
    
    print("\n测试文化符号分析工具...")
    analyzer_tool = agent.tools[3]  # CulturalSymbolAnalyzer
    
    test_info = json.dumps({
        "name": "测试公司",
        "materials": ["公司简介", "产品介绍"],
        "questions_answers": {"问题1": "答案1", "问题2": "答案2"}
    })
    
    symbols = analyzer_tool._run(test_info)
    print("分析结果:", symbols)

def main():
    """主函数"""
    print("🎯 文化符号分析Agent系统")
    print("请选择运行模式：")
    print("1. 演示模式 (demo)")
    print("2. 交互模式 (interactive)")
    print("3. 工具测试 (test)")
    print("4. 退出 (quit)")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1" or choice.lower() == "demo":
            demo_workflow()
            break
        elif choice == "2" or choice.lower() == "interactive":
            interactive_mode()
            break
        elif choice == "3" or choice.lower() == "test":
            test_tools()
            break
        elif choice == "4" or choice.lower() == "quit":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("KIMI_API_KEY"):
        print("❌ 错误: 请设置KIMI_API_KEY环境变量")
        print("💡 提示: 复制.env.example为.env并填入您的API密钥")
        exit(1)
    
    main()
