"""
配置文件
管理系统的各种配置参数
"""

import os
from typing import Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# 确保加载.env文件
load_dotenv()

@dataclass
class LLMConfig:
    """LLM配置"""
    model_name: str = "kimi-k2-0711-preview"
    api_key: str = "sk-7g3tSy9agawgEu7eHRHGjRsKMUCsgTq0oRKPOhgf2IcoDVex"
    base_url: str = "https://api.moonshot.cn/v1"
    temperature: float = 0.3
    max_tokens: int = 2000
    timeout: int = 30

@dataclass
class MemoryConfig:
    """记忆配置"""
    memory_key: str = "chat_history"
    return_messages: bool = True
    k: int = 3  # 保存最近3轮对话

@dataclass
class AgentConfig:
    """Agent配置"""
    verbose: bool = True
    handle_parsing_errors: bool = True
    max_iterations: int = 10
    early_stopping_method: str = "generate"

@dataclass
class SystemConfig:
    """系统配置"""
    debug: bool = False
    log_level: str = "INFO"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    supported_file_types: list = None
    
    def __post_init__(self):
        if self.supported_file_types is None:
            self.supported_file_types = [
                ".txt", ".md", ".pdf", ".doc", ".docx",
                ".jpg", ".jpeg", ".png", ".gif"
            ]

class Config:
    """主配置类"""
    
    def __init__(self):
        self.llm = LLMConfig()
        self.memory = MemoryConfig()
        self.agent = AgentConfig()
        self.system = SystemConfig()
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # LLM配置
        self.llm.api_key = os.getenv("KIMI_API_KEY", "")
        self.llm.base_url = os.getenv("KIMI_BASE_URL", self.llm.base_url)
        self.llm.model_name = os.getenv("KIMI_MODEL", self.llm.model_name)
        
        # 系统配置
        self.system.debug = os.getenv("DEBUG", "False").lower() == "true"
        self.system.log_level = os.getenv("LOG_LEVEL", self.system.log_level)
        
        # Agent配置
        self.agent.verbose = os.getenv("AGENT_VERBOSE", "True").lower() == "true"
        
        # 数值配置
        try:
            self.llm.temperature = float(os.getenv("LLM_TEMPERATURE", self.llm.temperature))
            self.llm.max_tokens = int(os.getenv("LLM_MAX_TOKENS", self.llm.max_tokens))
            self.memory.k = int(os.getenv("MEMORY_K", self.memory.k))
            self.agent.max_iterations = int(os.getenv("AGENT_MAX_ITERATIONS", self.agent.max_iterations))
        except ValueError as e:
            print(f"警告: 配置参数转换失败: {e}")
    
    def validate(self) -> bool:
        """验证配置"""
        errors = []
        
        # 检查必需的配置
        if not self.llm.api_key:
            errors.append("KIMI_API_KEY未设置")
        
        if not self.llm.base_url:
            errors.append("KIMI_BASE_URL未设置")
        
        # 检查数值范围
        if not 0 <= self.llm.temperature <= 2:
            errors.append("temperature必须在0-2之间")
        
        if self.llm.max_tokens <= 0:
            errors.append("max_tokens必须大于0")
        
        if self.memory.k <= 0:
            errors.append("memory.k必须大于0")
        
        if self.agent.max_iterations <= 0:
            errors.append("max_iterations必须大于0")
        
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "llm": {
                "model_name": self.llm.model_name,
                "base_url": self.llm.base_url,
                "temperature": self.llm.temperature,
                "max_tokens": self.llm.max_tokens,
                "timeout": self.llm.timeout
            },
            "memory": {
                "memory_key": self.memory.memory_key,
                "return_messages": self.memory.return_messages,
                "k": self.memory.k
            },
            "agent": {
                "verbose": self.agent.verbose,
                "handle_parsing_errors": self.agent.handle_parsing_errors,
                "max_iterations": self.agent.max_iterations,
                "early_stopping_method": self.agent.early_stopping_method
            },
            "system": {
                "debug": self.system.debug,
                "log_level": self.system.log_level,
                "max_file_size": self.system.max_file_size,
                "supported_file_types": self.system.supported_file_types
            }
        }
    
    def print_config(self):
        """打印配置信息"""
        print("📋 当前配置:")
        print("=" * 50)
        
        print("🤖 LLM配置:")
        print(f"  模型: {self.llm.model_name}")
        print(f"  API地址: {self.llm.base_url}")
        print(f"  温度: {self.llm.temperature}")
        print(f"  最大令牌: {self.llm.max_tokens}")
        print(f"  API密钥: {'已设置' if self.llm.api_key else '未设置'}")
        
        print("\n💾 记忆配置:")
        print(f"  保存轮数: {self.memory.k}")
        print(f"  返回消息: {self.memory.return_messages}")
        
        print("\n🔧 Agent配置:")
        print(f"  详细输出: {self.agent.verbose}")
        print(f"  最大迭代: {self.agent.max_iterations}")
        print(f"  错误处理: {self.agent.handle_parsing_errors}")
        
        print("\n⚙️ 系统配置:")
        print(f"  调试模式: {self.system.debug}")
        print(f"  日志级别: {self.system.log_level}")
        print(f"  最大文件大小: {self.system.max_file_size // 1024 // 1024}MB")
        print(f"  支持文件类型: {', '.join(self.system.supported_file_types[:5])}...")

# 全局配置实例
config = Config()

# 配置验证函数
def validate_config() -> bool:
    """验证全局配置"""
    return config.validate()

# 获取配置函数
def get_config() -> Config:
    """获取全局配置"""
    return config

# 配置更新函数
def update_config(**kwargs):
    """更新配置"""
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            print(f"警告: 未知配置项 {key}")

if __name__ == "__main__":
    # 测试配置
    config.print_config()
    print(f"\n✅ 配置验证: {'通过' if config.validate() else '失败'}")
