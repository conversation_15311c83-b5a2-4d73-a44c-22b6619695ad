"""
快速测试脚本
验证基本组件是否正常工作
"""

import os
import sys

def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        from cultural_symbol_agent import (
            CulturalSymbolAgent,
            CustomerInfoTool,
            WorkflowTool,
            QuestionGeneratorTool,
            CulturalSymbolAnalyzer,
            WorkflowState,
            CustomerInfo
        )
        print("✅ 主模块导入成功")
        
        from config import get_config
        print("✅ 配置模块导入成功")
        
        from logger import get_logger
        print("✅ 日志模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_tools():
    """测试工具功能"""
    print("\n🔧 测试工具功能...")
    
    try:
        from cultural_symbol_agent import (
            CustomerInfoTool,
            WorkflowTool,
            QuestionGeneratorTool,
            CulturalSymbolAnalyzer
        )
        
        # 测试客户信息工具
        customer_tool = CustomerInfoTool()
        result = customer_tool._run("set_name", "测试公司")
        print(f"✅ 客户信息工具: {result}")

        # 测试工作流程工具
        workflow_tool = WorkflowTool()
        result = workflow_tool._run("get_state")
        print(f"✅ 工作流程工具: {result}")

        # 测试问题生成工具
        question_tool = QuestionGeneratorTool()
        result = question_tool._run("测试公司")
        print(f"✅ 问题生成工具: 生成了问题")

        # 测试文化符号分析工具
        analyzer = CulturalSymbolAnalyzer()
        test_info = '{"name": "测试公司", "materials": ["资料1"], "questions_answers": {"问题1": "答案1"}}'
        result = analyzer._run(test_info)
        print(f"✅ 文化符号分析工具: 分析完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from config import get_config
        
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"  - LLM模型: {config.llm.model_name}")
        print(f"  - 记忆轮数: {config.memory.k}")
        print(f"  - API密钥: {'已设置' if config.llm.api_key else '未设置'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_logger():
    """测试日志"""
    print("\n📝 测试日志...")
    
    try:
        from logger import get_logger
        
        logger = get_logger()
        logger.info("测试日志消息")
        logger.debug("测试调试消息")
        logger.warning("测试警告消息")
        
        print("✅ 日志系统正常")
        return True
        
    except Exception as e:
        print(f"❌ 日志测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 文化符号分析Agent - 快速测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("工具功能", test_tools),
        ("配置系统", test_config),
        ("日志系统", test_logger)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔸 {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
